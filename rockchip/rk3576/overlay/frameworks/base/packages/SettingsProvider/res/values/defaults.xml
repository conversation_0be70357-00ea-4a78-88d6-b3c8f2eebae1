<?xml version="1.0" encoding="utf-8"?>
<!--
/**
 * Copyright (c) 2009, The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
-->
<resources>
    <integer name="def_screen_off_timeout">900000</integer>
   <!-- Initial value for the Settings.Secure.IMMERSIVE_MODE_CONFIRMATIONS setting,
         which is a comma separated list of packages that no longer need confirmation
         for immersive mode.
         Override to disable immersive mode confirmation for certain packages. -->
    <string name="def_immersive_mode_confirmations" translatable="false">confirmed</string>
    <bool name="def_accelerometer_rotation">false</bool>

    <!-- Disable the lockscreen -->
    <bool name="def_lockscreen_disabled">true</bool>
    <!-- No setup wizard -->
    <bool name="def_device_provisioned">true</bool>
    <!-- 0 == Always sleep
	 1 == Do not sleep when plugged in
	 2 == Never sleep
	When the screen is off, it will enter the sleep policy.
	You can configure persist.wifi.sleep.delay.ms to delay closing wifi.
	The default is 30 seconds, 0 means that the wifi is turned off
	immediately after the screen is off. -->
    <integer name="def_wifi_sleep_policy">2</integer>
    <bool name="def_bluetooth_on">true</bool>
    <bool name="def_wifi_on">true</bool>

    <!-- Decrease animation duration. -->
    <fraction name="def_window_animation_scale">100%</fraction>
    <fraction name="def_window_transition_scale">100%</fraction>
    <fraction name="def_animator_duration_scale">100%</fraction>

    <!-- czur config default input method -->
    <add-resource type="string" name="config_default_input_method" />
    <add-resource type="string" name="config_enabled_input_methods" />
    <string name="config_default_input_method" translatable="false">com.google.android.inputmethod.latin/.LatinIME</string>
    <string name="config_enabled_input_methods" translatable="false">com.google.android.inputmethod.latin/.LatinIME</string>

    <!-- default time format as 24 hours -->
    <add-resource type="string" name="time_12_24" />
    <string name="time_12_24" translatable="false">24</string>
    <integer name="def_screen_brightness">255</integer>
</resources>
