// SPDX-License-Identifier: GPL-2.0
/*
 * Copyright (c) 2022 Rockchip Electronics Co. Ltd.
 *
 * lt6911uxe HDMI to MIPI CSI-2 bridge driver.
 *
 * Author: <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * V0.0X01.0X00 first version.
 * V0.0X01.0X01 support DPHY 4K60.
 * V0.0X01.0X02 support BGR888 format.
 * V0.0X01.0X03 add more timing support.
 * V0.0X01.0X04
 *  1.fix some errors.
 *  2.add dphy timing reg.
 * V0.0X01.0X05 add dual mipi mode support
 * V0.0X01.0X06 add yuv420 8bit
 *
 */
// #define DEBUG
#include <linux/clk.h>
#include <linux/delay.h>
#include <linux/gpio/consumer.h>
#include <linux/hdmi.h>
#include <linux/i2c.h>
#include <linux/interrupt.h>
#include <linux/kernel.h>
#include <linux/module.h>
#include <linux/of_graph.h>
#include <linux/slab.h>
#include <linux/timer.h>
#include <linux/version.h>
#include <linux/workqueue.h>
#include <linux/compat.h>
#include <sound/soc.h>
#include <linux/gpio_keys.h>
#include <linux/extcon.h>
#include <linux/extcon-provider.h>
#include <linux/of_gpio.h>
#include <linux/input.h>
#include <sound/pcm.h>
#include <sound/pcm_params.h>
#include "lt6911uxe.h"

#define DRIVER_VERSION			KERNEL_VERSION(0, 0x01, 0x06)

static int debug;
module_param(debug, int, 0644);
MODULE_PARM_DESC(debug, "debug level (0-3)");

/* 全局变量，用于保存 PCM 子流指针 */
static struct snd_pcm_substream *g_pcm_substream;
/* 全局变量，用于标记 PCM 流是否活动 */
static atomic_t g_pcm_active = ATOMIC_INIT(0);

enum lt6911uxe_bus_fmt {
	RGB_6Bit = 0,
	RGB_8Bit,
	RGB_10Bit,
	RGB_12Bit,
	YUV444_8Bit,
	YUV444_10Bit,
	YUV444_12Bit,
	YUV422_8Bit,
	YUV422_10Bit,
	YUV422_12Bit,
	YUV420_8Bit,
	YUV420_10Bit,
	YUV420_12Bit,
};

static const char * const bus_format_str[] = {
	"RGB_6Bit",
	"RGB_8Bit",
	"RGB_10Bit",
	"RGB_12Bit",
	"YUV444_8Bit",
	"YUV444_10Bit",
	"YUV444_12Bit",
	"YUV422_8Bit",
	"YUV422_10Bit",
	"YUV422_12Bit",
	"YUV420_8Bit",
	"YUV420_10Bit",
	"YUV420_12Bit",
	"UNKNOWN",
};
#ifdef LT6911UXE_OUT_RGB
static const s64 link_freq_menu_items[] = {
	LT6911UXE_LINK_FREQ_1250M,
	LT6911UXE_LINK_FREQ_900M,
	LT6911UXE_LINK_FREQ_600M,
	LT6911UXE_LINK_FREQ_450M,
	LT6911UXE_LINK_FREQ_300M,
	LT6911UXE_LINK_FREQ_150M,
};
#else
static const s64 link_freq_menu_items[] = {
	LT6911UXE_LINK_FREQ_1250M,
	LT6911UXE_LINK_FREQ_600M,
	LT6911UXE_LINK_FREQ_500M,
	LT6911UXE_LINK_FREQ_400M,
	LT6911UXE_LINK_FREQ_300M,
	LT6911UXE_LINK_FREQ_200M,
	LT6911UXE_LINK_FREQ_100M,
};
#endif

static const struct v4l2_dv_timings_cap lt6911uxe_timings_cap = {
	.type = V4L2_DV_BT_656_1120,
	.reserved = { 0 },
	V4L2_INIT_BT_TIMINGS(1, 10000, 1, 10000, 0, 800000000,
			V4L2_DV_BT_STD_CEA861 | V4L2_DV_BT_STD_DMT |
			V4L2_DV_BT_STD_GTF | V4L2_DV_BT_STD_CVT,
			V4L2_DV_BT_CAP_PROGRESSIVE | V4L2_DV_BT_CAP_INTERLACED |
			V4L2_DV_BT_CAP_REDUCED_BLANKING |
			V4L2_DV_BT_CAP_CUSTOM)
};

struct lt6911uxe_mode {
	u32 width;
	u32 height;
	struct v4l2_fract max_fps;
	u32 hts_def;
	u32 vts_def;
	u32 exp_def;
	u32 mipi_freq_idx;
	u32 interlace;
};

static struct rkmodule_csi_dphy_param rk3588_dcphy_param = {
	.vendor = PHY_VENDOR_SAMSUNG,
	.lp_vol_ref = 3,
	.lp_hys_sw = {3, 0, 3, 0},
	.lp_escclk_pol_sel = {1, 1, 0, 0},
	.skew_data_cal_clk = {0, 13, 0, 13},
	.clk_hs_term_sel = 2,
	.data_hs_term_sel = {2, 2, 2, 2},
	.reserved = {0},
};

static const struct lt6911uxe_mode supported_modes_dphy[] = {
	/*{
		.width = 5120,
		.height = 2160,
		.max_fps = {
			.numerator = 10000,
			.denominator = 480000,
		},
		.hts_def = 5500,
		.vts_def = 2250,
		.mipi_freq_idx = 0,
		.interlace = 0,
	}, {
		.width = 4096,
		.height = 2160,
		.max_fps = {
			.numerator = 10000,
			.denominator = 600000,
		},
		.hts_def = 4400,
		.vts_def = 2250,
		.mipi_freq_idx = 0,
		.interlace = 0,
	}, {
		.width = 4096,
		.height = 2160,
		.max_fps = {
			.numerator = 10000,
			.denominator = 300000,
		},
		.hts_def = 4400,
		.vts_def = 2250,
		.mipi_freq_idx = 1,
		.interlace = 0,
	},*/ {
		.width = 3840,
		.height = 2160,
		.max_fps = {
			.numerator = 10000,
			.denominator = 600000,
		},
		.hts_def = 4400,
		.vts_def = 2250,
		.mipi_freq_idx = 0,
		.interlace = 0,
	}, {
		.width = 3840,
		.height = 2160,
		.max_fps = {
			.numerator = 10000,
			.denominator = 300000,
		},
		.hts_def = 4400,
		.vts_def = 2250,
		.mipi_freq_idx = 1,
		.interlace = 0,
	}, {
		.width = 2560,
		.height = 1440,
		.max_fps = {
			.numerator = 10000,
			.denominator = 600000,
		},
		.hts_def = 2720,
		.vts_def = 1481,
		.mipi_freq_idx = 2,
		.interlace = 0,
	}, {
		.width = 2560,
		.height = 1440,
		.max_fps = {
			.numerator = 10000,
			.denominator = 300000,
		},
		.hts_def = 2720,
		.vts_def = 1481,
		.mipi_freq_idx = 4,
		.interlace = 0,
	}, {
		.width = 1920,
		.height = 1200,
		.max_fps = {
			.numerator = 10000,
			.denominator = 600000,
		},
		.hts_def = 2592,
		.vts_def = 1245,
		.mipi_freq_idx = 3,
		.interlace = 0,
	}, {
		.width = 1920,
		.height = 1200,
		.max_fps = {
			.numerator = 10000,
			.denominator = 300000,
		},
		.hts_def = 2592,
		.vts_def = 1245,
		.mipi_freq_idx = 5,
		.interlace = 0,
	}, {
		.width = 1920,
		.height = 1080,
		.max_fps = {
			.numerator = 10000,
			.denominator = 600000,
		},
		.hts_def = 2200,
		.vts_def = 1125,
		.mipi_freq_idx = 4,
		.interlace = 0,
	},{
		.width = 1920,
		.height = 1080,
		.max_fps = {
			.numerator = 10000,
			.denominator = 300000,
		},
		.hts_def = 2200,
		.vts_def = 1125,
		.mipi_freq_idx = 5,
		.interlace = 0,
	}, {
		.width = 1680,
		.height = 1050,
		.max_fps = {
			.numerator = 10000,
			.denominator = 600000,
		},
		.hts_def = 2240,
		.vts_def = 1089,
		.mipi_freq_idx = 4,
		.interlace = 0,
	}, {
		.width = 1600,
		.height = 1200,
		.max_fps = {
			.numerator = 10000,
			.denominator = 600000,
		},
		.hts_def = 2160,
		.vts_def = 1250,
		.mipi_freq_idx = 4,
		.interlace = 0,
	}, {
		.width = 1600,
		.height = 900,
		.max_fps = {
			.numerator = 10000,
			.denominator = 600000,
		},
		.hts_def = 1800,
		.vts_def = 1000,
		.mipi_freq_idx = 4,
		.interlace = 0,
	}, {
		.width = 1440,
		.height = 900,
		.max_fps = {
			.numerator = 10000,
			.denominator = 600000,
		},
		.hts_def = 1904,
		.vts_def = 934,
		.mipi_freq_idx = 4,
		.interlace = 0,
	}, /*{
		.width = 1440,
		.height = 240,
		.max_fps = {
			.numerator = 10000,
			.denominator = 600000,
		},
		.hts_def = 1716,
		.vts_def = 262,
		.mipi_freq_idx = 6,
		.interlace = 0,
	}, */{
		.width = 1360,
		.height = 768,
		.max_fps = {
			.numerator = 10000,
			.denominator = 600000,
		},
		.hts_def = 1792,
		.vts_def = 795,
		.mipi_freq_idx = 5,
		.interlace = 0,
	}, {
		.width = 1280,
		.height = 1024,
		.max_fps = {
			.numerator = 10000,
			.denominator = 600000,
		},
		.hts_def = 1688,
		.vts_def = 1066,
		.mipi_freq_idx = 4,
		.interlace = 0,
	}, {
		.width = 1280,
		.height = 960,
		.max_fps = {
			.numerator = 10000,
			.denominator = 600000,
		},
		.hts_def = 1712,
		.vts_def = 994,
		.mipi_freq_idx = 4,
		.interlace = 0,
	}, {
		.width = 1280,
		.height = 800,
		.max_fps = {
			.numerator = 10000,
			.denominator = 600000,
		},
		.hts_def = 1680,
		.vts_def = 828,
		.mipi_freq_idx = 5,
		.interlace = 0,
	}, {
		.width = 1280,
		.height = 768,
		.max_fps = {
			.numerator = 10000,
			.denominator = 600000,
		},
		.hts_def = 1664,
		.vts_def = 798,
		.mipi_freq_idx = 5,
		.interlace = 0,
	}, {
		.width = 1280,
		.height = 720,
		.max_fps = {
			.numerator = 10000,
			.denominator = 600000,
		},
		.hts_def = 1650,
		.vts_def = 750,
		.mipi_freq_idx = 5,
		.interlace = 0,
	}, {
		.width = 1152,
		.height = 864,
		.max_fps = {
			.numerator = 10000,
			.denominator = 750000,
		},
		.hts_def = 1600,
		.vts_def = 900,
		.mipi_freq_idx = 5,
		.interlace = 0,
	}, {
		.width = 1024,
		.height = 768,
		.max_fps = {
			.numerator = 10000,
			.denominator = 600000,
		},
		.hts_def = 1344,
		.vts_def = 806,
		.mipi_freq_idx = 5,
		.interlace = 0,
	}, {
		.width = 800,
		.height = 600,
		.max_fps = {
			.numerator = 10000,
			.denominator = 600000,
		},
		.hts_def = 1056,
		.vts_def = 628,
		.mipi_freq_idx = 6,
		.interlace = 0,
	}, {
		.width = 720,
		.height = 576,
		.max_fps = {
			.numerator = 10000,
			.denominator = 500000,
		},
		.hts_def = 864,
		.vts_def = 625,
		.mipi_freq_idx = 6,
		.interlace = 0,
	}, {
		.width = 720,
		.height = 480,
		.max_fps = {
			.numerator = 10000,
			.denominator = 600000,
		},
		.hts_def = 858,
		.vts_def = 525,
		.mipi_freq_idx = 6,
		.interlace = 0,
	}, /*{
		.width = 720,
		.height = 400,
		.max_fps = {
			.numerator = 10000,
			.denominator = 850000,
		},
		.hts_def = 936,
		.vts_def = 446,
		.mipi_freq_idx = 6,
		.interlace = 0,
	}, {
		.width = 720,
		.height = 240,
		.max_fps = {
			.numerator = 10000,
			.denominator = 600000,
		},
		.mipi_freq_idx = 6,
		.interlace = 0,
	}, */{
		.width = 640,
		.height = 480,
		.max_fps = {
			.numerator = 10000,
			.denominator = 600000,
		},
		.hts_def = 800,
		.vts_def = 525,
		.mipi_freq_idx = 6,
		.interlace = 0,
	},
};


static ssize_t check_audio_present(struct device *dev, struct device_attribute *attr, char *buf)
{
	//struct lt6911uxe *state = dev_get_drvdata(dev);
	struct lt6911uxe *state = container_of(i2c_get_clientdata(to_i2c_client(dev)), struct lt6911uxe, sd);
	//printk("state->is_audio_present: %d", state->is_audio_present);
	return snprintf(buf, PAGE_SIZE, "%d\n", state->is_audio_present);
}

static ssize_t check_audio_sample_rate(struct device *dev, struct device_attribute *attr, char *buf)
{
	//struct lt6911uxe *state = dev_get_drvdata(dev);
	struct lt6911uxe *state = container_of(i2c_get_clientdata(to_i2c_client(dev)), struct lt6911uxe, sd);
	//state->audio_sampling_rate = 48000;
    //printk("state->audio_sample_rate: %d", state->audio_sample_rate);
	return snprintf(buf, PAGE_SIZE, "%d\n", state->audio_sampling_rate);
}

static ssize_t lt6911uxe_upgrade_show(
    struct device *dev, struct device_attribute *attr, char *buf)
{
    return -EPERM;
}

static ssize_t lt6911uxe_upgrade_store(
    struct device *dev,
    struct device_attribute *attr, const char *buf, size_t count)
{
    char fwname[FILE_NAME_LENGTH];
	//struct lt6911uxe *lt6911uxe = dev_get_drvdata(dev);
	struct lt6911uxe *lt6911uxe = container_of(i2c_get_clientdata(to_i2c_client(dev)), struct lt6911uxe, sd);

    if ((count <= 1) || (count >= FILE_NAME_LENGTH - 32)) {
        LT6911UXE_ERR("fw bin name's length(%d) fail", (int)count);
        return -EINVAL;
    }
    memset(fwname, 0, sizeof(fwname));
    snprintf(fwname, FILE_NAME_LENGTH, "%s", buf);
    fwname[count - 1] = '\0';

    LT6911UXE_INFO("force upgrade through sysfs node, fwname: %s", fwname);

    mutex_lock(&lt6911uxe->confctl_mutex);
    lt6911uxe_upgrade_bin(dev, fwname, 1);
    mutex_unlock(&lt6911uxe->confctl_mutex);

    return count;
}

static DEVICE_ATTR(audio_present, S_IRUGO | S_IWUSR, check_audio_present, NULL);
static DEVICE_ATTR(audio_sample_rate, S_IRUGO | S_IWUSR, check_audio_sample_rate, NULL);
static DEVICE_ATTR(lt6911uxe_upgrade_fw, S_IRUGO | S_IWUSR, lt6911uxe_upgrade_show, lt6911uxe_upgrade_store);

static struct attribute *lt6911uxe_attributes[] = {
    &dev_attr_audio_present.attr,
    &dev_attr_audio_sample_rate.attr,
    //&dev_attr_lt6911uxe_fw_version.attr,
    &dev_attr_lt6911uxe_upgrade_fw.attr,
	NULL
};

static struct attribute_group lt6911uxe_attribute_group = {
    .attrs	= lt6911uxe_attributes,
};


static void lt6911uxe_format_change(struct v4l2_subdev *sd);
static int lt6911uxe_s_ctrl_detect_tx_5v(struct v4l2_subdev *sd);
static int lt6911uxe_s_dv_timings(struct v4l2_subdev *sd,
				struct v4l2_dv_timings *timings);

static inline struct lt6911uxe *to_lt6911uxe(struct v4l2_subdev *sd)
{
	return container_of(sd, struct lt6911uxe, sd);
}

static void i2c_rd(struct v4l2_subdev *sd, u16 reg, u8 *values, u32 n)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);
	struct i2c_client *client = lt6911uxe->i2c_client;
	int err;
	u8 buf[2] = { 0xFF, reg >> 8};
	u8 reg_addr = reg & 0xFF;
	struct i2c_msg msgs[3];

	msgs[0].addr = client->addr;
	msgs[0].flags = 0;
	msgs[0].len = 2;
	msgs[0].buf = buf;

	msgs[1].addr = client->addr;
	msgs[1].flags = 0;
	msgs[1].len = 1;
	msgs[1].buf = &reg_addr;

	msgs[2].addr = client->addr;
	msgs[2].flags = I2C_M_RD;
	msgs[2].len = n;
	msgs[2].buf = values;

	err = i2c_transfer(client->adapter, msgs, ARRAY_SIZE(msgs));
	if (err != ARRAY_SIZE(msgs)) {
		v4l2_err(sd, "%s: reading register 0x%x from 0x%x failed\n",
				__func__, reg, client->addr);
	}

	if (!debug)
		return;

	switch (n) {
	case 1:
		v4l2_info(sd, "I2C read 0x%04x = 0x%02x\n",
			reg, values[0]);
		break;
	case 2:
		v4l2_info(sd, "I2C read 0x%04x = 0x%02x%02x\n",
			reg, values[1], values[0]);
		break;
	case 4:
		v4l2_info(sd, "I2C read 0x%04x = 0x%02x%02x%02x%02x\n",
			reg, values[3], values[2], values[1], values[0]);
		break;
	default:
		v4l2_info(sd, "I2C read %d bytes from address 0x%04x\n",
			n, reg);
	}
}

static void i2c_wr(struct v4l2_subdev *sd, u16 reg, u8 *values, u32 n)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);
	struct i2c_client *client = lt6911uxe->i2c_client;
	int err, i;
	struct i2c_msg msgs[2];
	u8 data[I2C_MAX_XFER_SIZE];
	u8 buf[2] = { 0xFF, reg >> 8};

	if ((1 + n) > I2C_MAX_XFER_SIZE) {
		n = I2C_MAX_XFER_SIZE - 1;
		v4l2_warn(sd, "i2c wr reg=%04x: len=%d is too big!\n",
			  reg, 1 + n);
	}

	msgs[0].addr = client->addr;
	msgs[0].flags = 0;
	msgs[0].len = 2;
	msgs[0].buf = buf;

	msgs[1].addr = client->addr;
	msgs[1].flags = 0;
	msgs[1].len = 1 + n;
	msgs[1].buf = data;

	data[0] = reg & 0xff;
	for (i = 0; i < n; i++)
		data[1 + i] = values[i];

	err = i2c_transfer(client->adapter, msgs, ARRAY_SIZE(msgs));
	if (err < 0) {
		v4l2_err(sd, "%s: writing register 0x%x from 0x%x failed\n",
				__func__, reg, client->addr);
		return;
	}

	if (!debug)
		return;

	switch (n) {
	case 1:
		v4l2_info(sd, "I2C write 0x%04x = 0x%02x\n",
				reg, data[1]);
		break;
	case 2:
		v4l2_info(sd, "I2C write 0x%04x = 0x%02x%02x\n",
				reg, data[2], data[1]);
		break;
	case 4:
		v4l2_info(sd, "I2C write 0x%04x = 0x%02x%02x%02x%02x\n",
				reg, data[4], data[3], data[2], data[1]);
		break;
	default:
		v4l2_info(sd, "I2C write %d bytes from address 0x%04x\n",
				n, reg);
	}
}

static u8 i2c_rd8(struct v4l2_subdev *sd, u16 reg)
{
	u32 val;

	i2c_rd(sd, reg, (u8 __force *)&val, 1);
	return val;
}

static void i2c_wr8(struct v4l2_subdev *sd, u16 reg, u8 val)
{
	i2c_wr(sd, reg, &val, 1);
}

static __maybe_unused void i2c_wr8_and_or(struct v4l2_subdev *sd, u16 reg, u32 mask,
			   u8 val)
{
	u8 val_p;

	val_p = i2c_rd8(sd, reg);
	i2c_wr8(sd, reg, (val_p & mask) | val);
}

static void lt6911uxe_i2c_enable(struct v4l2_subdev *sd)
{
	i2c_wr8(sd, I2C_EN_REG, I2C_ENABLE);
}

static void lt6911uxe_i2c_disable(struct v4l2_subdev *sd)
{
	i2c_wr8(sd, I2C_EN_REG, I2C_DISABLE);
}

static inline bool tx_5v_power_present(struct v4l2_subdev *sd)
{
	bool ret;
	int val, i, cnt;
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);

	/* if not use plugin det gpio */
	if (!lt6911uxe->plugin_det_gpio)
		return true;

	cnt = 0;
	for (i = 0; i < 5; i++) {
		val = gpiod_get_value(lt6911uxe->plugin_det_gpio);
		if (val > 0)
			cnt++;
		usleep_range(500, 600);
	}

	ret = (cnt >= 4) ? true : false;
	v4l2_dbg(1, debug, sd, "%s: %d\n", __func__, ret);

	return ret;
}

static inline bool no_signal(struct v4l2_subdev *sd)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);

	v4l2_dbg(1, debug, sd, "%s no signal:%d\n", __func__,
			lt6911uxe->nosignal);

	return lt6911uxe->nosignal;
}

static const unsigned int lt6911uxe_hdmi_cable[] = {
	EXTCON_JACK_VIDEO_IN,
	EXTCON_NONE,
};

static int snd_lt6911uxe_dai_hw_params(struct snd_pcm_substream *substream,
                       struct snd_pcm_hw_params *params,
                       struct snd_soc_dai *codec_dai)
{
#if 0 //do nothing temp.
    struct snd_soc_component *component = codec_dai->component;
    struct lt6911uxe_state *state = snd_soc_component_get_drvdata(component);
    unsigned int fs;

    switch (params_rate(params)) {
    case 32000:
        fs = FS_32000;
        break;
    case 44100:
        fs = FS_44100;
        break;
    case 48000:
        fs = FS_48000;
        break;
    case 88200:
        fs = FS_88200;
        break;
    case 96000:
        fs = FS_96000;
        break;
    case 176400:
        fs = FS_176400;
        break;
    case 192000:
        fs = FS_192000;
        break;
    default:
        dev_err(component->dev, "Enter:%s, %d, Error rate=%d\n",
            __func__, __LINE__, params_rate(params));
        return -EINVAL;
    }
    dev_err(component->dev, "Enter:%s, %d, Error rate=%d\n",
            __func__, __LINE__, params_rate(params));
    //snd_soc_component_update_bits(component, LT6911UXE_FS_SET, FS_SET_MASK, fs);
    i2c_wr8(&state->sd, LT6911UXE_FS_SET, FS_SET_MASK & fs);
#endif
    return 0;
}

static int snd_lt6911uxe_dai_startup(struct snd_pcm_substream *substream,
                       struct snd_soc_dai *dai)
{
    struct snd_soc_component *component = dai->component;
    struct lt6911uxe *lt6911uxe = snd_soc_component_get_drvdata(component);

    /* 保存PCM子流指针到全局变量，以便在HDMI断开时能够通知ALSA框架 */
    g_pcm_substream = substream;
    lt6911uxe->pcm_substream = substream;

    /* 设置 PCM 活动状态 */
    atomic_set(&g_pcm_active, 1);
    atomic_set(&lt6911uxe->pcm_active, 1);

    dev_info(component->dev, "%s: PCM stream started, substream=%p\n", __func__, substream);

    /* 打印当前线程信息，以便调试 */
    dev_info(component->dev, "%s: current process: %s (%d)\n", __func__,
             current->comm, current->pid);

    return 0;
}

static void snd_lt6911uxe_dai_shutdown(struct snd_pcm_substream *substream,
                       struct snd_soc_dai *dai)
{
    struct snd_soc_component *component = dai->component;
    struct lt6911uxe *lt6911uxe = snd_soc_component_get_drvdata(component);

    /* 打印当前线程信息，以便调试 */
    dev_info(component->dev, "%s: current process: %s (%d)\n", __func__,
             current->comm, current->pid);

    /* 打印当前子流指针和存储的子流指针 */
    dev_info(component->dev, "%s: current substream=%p, stored substream=%p\n",
             __func__, substream, lt6911uxe->pcm_substream);

    /* 清除PCM子流指针 */
    g_pcm_substream = NULL;
    lt6911uxe->pcm_substream = NULL;

    /* 设置 PCM 活动状态为非活动 */
    atomic_set(&g_pcm_active, 0);
    atomic_set(&lt6911uxe->pcm_active, 0);

    dev_info(component->dev, "%s: PCM stream stopped\n", __func__);
}

#if 0
static int snd_lt6911uxe_mute(struct snd_soc_dai *dai, int mute)
{
    return 0;
}
#endif

static const struct snd_soc_dai_ops lt6911uxe_dai_ops = {
    .hw_params = snd_lt6911uxe_dai_hw_params,
    .startup = snd_lt6911uxe_dai_startup,
    .shutdown = snd_lt6911uxe_dai_shutdown,
    //.digital_mute = snd_lt6911uxe_mute,
};

static struct snd_soc_dai_driver lt6911uxe_dai = {
    .name = "lt6911uxe-audio",
    .capture = {
        .stream_name = "Capture",
        .channels_min = 2,
        .channels_max = 8,
        .rates = SNDRV_PCM_RATE_32000 |
             SNDRV_PCM_RATE_44100 | SNDRV_PCM_RATE_48000 |
             SNDRV_PCM_RATE_88200 | SNDRV_PCM_RATE_96000 |
             SNDRV_PCM_RATE_176400 | SNDRV_PCM_RATE_192000,
        .formats = SNDRV_PCM_FMTBIT_S16_LE | SNDRV_PCM_FMTBIT_S24_LE,
    },
    .ops = &lt6911uxe_dai_ops,
};

static int lt6911uxe_set_bias_level(struct snd_soc_component *component,
                   enum snd_soc_bias_level level)
{
    //struct lt6911uxe_state *state = snd_soc_component_get_drvdata(component);
    switch (level) {
    case SND_SOC_BIAS_ON:
        break;

    case SND_SOC_BIAS_PREPARE:
        //snd_soc_component_update_bits(component, FORCE_MUTE,
        //            FORCE_DMUTE_MASK, !MUTE);
        //2c_wr8(&state->sd, FORCE_MUTE, FORCE_DMUTE_MASK & (!MUTE));
        break;

    case SND_SOC_BIAS_STANDBY:
        break;

    case SND_SOC_BIAS_OFF:
        //snd_soc_component_update_bits(component, FORCE_MUTE,
        //            FORCE_DMUTE_MASK, MUTE);
        //i2c_wr8(&state->sd, FORCE_MUTE, FORCE_DMUTE_MASK & MUTE);
        break;
    }

    return 0;
}

static int lt6911uxe_soc_probe(struct snd_soc_component *component)
{
    return 0;
    //return lt6911uxe_set_bias_level(component, SND_SOC_BIAS_OFF);
}

#ifdef CONFIG_PM
static int lt6911uxe_suspend(struct snd_soc_component *component)
{
	return 0;
}

static int lt6911uxe_resume(struct snd_soc_component *component)
{
	return 0;
}
#endif

static struct snd_soc_component_driver soc_codec_dev_lt6911uxe = {
    .probe = lt6911uxe_soc_probe,
    .set_bias_level = lt6911uxe_set_bias_level,
    #ifdef CONFIG_PM
    .suspend = lt6911uxe_suspend,
    .resume = lt6911uxe_resume,
    #endif
};

static inline bool audio_present(struct v4l2_subdev *sd)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);

	return lt6911uxe->is_audio_present;
}

static int get_audio_sampling_rate(struct v4l2_subdev *sd)
{
	static const int code_to_rate[] = {
		44100, 0, 48000, 32000, 22050, 384000, 24000, 352800,
		88200, 768000, 96000, 705600, 176400, 0, 192000, 0
	};

	if (no_signal(sd))
		return 0;

	return code_to_rate[2];
}

static inline unsigned int fps_calc(const struct v4l2_bt_timings *t)
{
	if (!V4L2_DV_BT_FRAME_HEIGHT(t) || !V4L2_DV_BT_FRAME_WIDTH(t))
		return 0;

	return DIV_ROUND_CLOSEST((unsigned int)t->pixelclock,
			V4L2_DV_BT_FRAME_HEIGHT(t) * V4L2_DV_BT_FRAME_WIDTH(t));
}

static bool lt6911uxe_rcv_supported_res(struct v4l2_subdev *sd, u32 width,
		u32 height)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);
	u32 i;

	for (i = 0; i < lt6911uxe->cfg_num; i++) {
		if ((lt6911uxe->support_modes[i].width == width) &&
		    (lt6911uxe->support_modes[i].height == height)) {
			break;
		}
	}

	if (i == lt6911uxe->cfg_num) {
		v4l2_err(sd, "%s do not support res wxh: %dx%d\n", __func__,
				width, height);
		return false;
	} else {
		return true;
	}
}

static int lt6911uxe_get_detected_timings(struct v4l2_subdev *sd,
				     struct v4l2_dv_timings *timings)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);
	struct v4l2_bt_timings *bt = &timings->bt;
	u32 hact, vact, htotal, vtotal, hs, vs, hbp, vbp, hfp, vfp;
	u32 pixel_clock, halt_pix_clk;
	u8 clk_h, clk_m, clk_l;
	u8 val_h, val_l;
	u32 byte_clk, mipi_clk, mipi_data_rate;

	memset(timings, 0, sizeof(struct v4l2_dv_timings));

	clk_h = i2c_rd8(sd, PCLK_H);
	clk_m = i2c_rd8(sd, PCLK_M);
	clk_l = i2c_rd8(sd, PCLK_L);
	halt_pix_clk = ((clk_h << 16) | (clk_m << 8) | clk_l);
	pixel_clock = halt_pix_clk * 1000 * 2;

	clk_h = i2c_rd8(sd, BYTE_PCLK_H);
	clk_m = i2c_rd8(sd, BYTE_PCLK_M);
	clk_l = i2c_rd8(sd, BYTE_PCLK_L);
	byte_clk = ((clk_h << 16) | (clk_m << 8) | clk_l) * 1000;
	mipi_clk = byte_clk * 4;
	mipi_data_rate = byte_clk * 8;

	val_h = i2c_rd8(sd, HTOTAL_H);
	val_l = i2c_rd8(sd, HTOTAL_L);
	htotal = ((val_h << 8) | val_l) * 2;

	val_h = i2c_rd8(sd, VTOTAL_H);
	val_l = i2c_rd8(sd, VTOTAL_L);
	vtotal = (val_h << 8) | val_l;

	val_h = i2c_rd8(sd, HACT_H);
	val_l = i2c_rd8(sd, HACT_L);
	hact = ((val_h << 8) | val_l) * 2;

	val_h = i2c_rd8(sd, VACT_H);
	val_l = i2c_rd8(sd, VACT_L);
	vact = (val_h << 8) | val_l;

	hs = i2c_rd8(sd, HS_HALF) * 2;

	val_h = i2c_rd8(sd, HFP_HALF_H);
	val_l = i2c_rd8(sd, HFP_HALF_L);
	hfp = ((val_h << 8) | val_l) * 2;

	hbp = htotal - hact - hs - hfp;

	vs = i2c_rd8(sd, VS);
	val_h = i2c_rd8(sd, VFP_H);
	val_l = i2c_rd8(sd, VFP_L);
	vfp = (val_h << 8) | val_l;

	vbp = vtotal - vact - vs - vfp;
	lt6911uxe->bus_fmt = i2c_rd8(sd, BUS_FMT);
	if (lt6911uxe->bus_fmt == YUV420_8Bit) {
		hact *= 2;
		hs *= 2;
		hfp *= 2;
		hbp *= 2;
		htotal *= 2;
		pixel_clock *= 2;
	}
	lt6911uxe->nosignal = false;
	// extcon_set_state_sync(lt6911uxe->extcon, EXTCON_JACK_VIDEO_IN, true);
	timings->type = V4L2_DV_BT_656_1120;
	bt->interlaced = V4L2_DV_PROGRESSIVE;
	bt->width = hact;
	bt->height = vact;
	bt->vsync = vs;
	bt->hsync = hs;
	bt->hfrontporch = hfp;
	bt->vfrontporch = vfp;
	bt->hbackporch = hbp;
	bt->vbackporch = vbp;
	bt->pixelclock = pixel_clock;
	lt6911uxe->cur_fps = pixel_clock / (htotal * vtotal);

	/* for interlaced res 1080i 576i 480i*/
	if ((hact == 1920 && vact == 540) || (hact == 1440 && vact == 288)
			|| (hact == 1440 && vact == 240)) {
		bt->interlaced = V4L2_DV_INTERLACED;
		bt->height *= 2;
		bt->il_vsync = bt->vsync + 1;
	} else {
		bt->interlaced = V4L2_DV_PROGRESSIVE;
	}

	v4l2_info(sd, "zhg++, pre act:%dx%d, total:%dx%d, pixclk:%d, fps:%d\n",
			hact, vact, htotal, vtotal, pixel_clock, lt6911uxe->cur_fps);

	if (lt6911uxe->bus_fmt == YUV420_8Bit) {
		lt6911uxe->mbus_fmt_code = MEDIA_BUS_FMT_UV8_1X8;
	} else {
		if (lt6911uxe->rgb_out)
			lt6911uxe->mbus_fmt_code = MEDIA_BUS_FMT_BGR888_1X24;
		else
			lt6911uxe->mbus_fmt_code = MEDIA_BUS_FMT_UYVY8_2X8;
	}

	if (!lt6911uxe_rcv_supported_res(sd, bt->width, bt->height)) {
		lt6911uxe->nosignal = true;
		v4l2_err(sd, "%s: rcv err res, return no signal!\n", __func__);
	}

	v4l2_info(sd, "act:%dx%d, total:%dx%d, pixclk:%u, fps:%d, bus fmt:%s\n",
				hact, vact, htotal, vtotal, pixel_clock,
				lt6911uxe->cur_fps, bus_format_str[lt6911uxe->bus_fmt]);
	v4l2_info(sd, "byte_clk:%u, mipi_clk:%u, mipi_data_rate:%u\n",
			byte_clk, mipi_clk, mipi_data_rate);
	v4l2_info(sd, "hfp:%d, hs:%d, hbp:%d, vfp:%d, vs:%d, vbp:%d, inerlaced:%d\n",
			bt->hfrontporch, bt->hsync, bt->hbackporch, bt->vfrontporch,
			bt->vsync, bt->vbackporch, bt->interlaced);

	return 0;
}

static void lt6911uxe_delayed_work_hotplug(struct work_struct *work)
{
	struct delayed_work *dwork = to_delayed_work(work);
	struct lt6911uxe *lt6911uxe = container_of(dwork,
			struct lt6911uxe, delayed_work_hotplug);
	struct v4l2_subdev *sd = &lt6911uxe->sd;
	int present;

	/* 获取当前的插入状态 */
	present = tx_5v_power_present(sd);

	/* 如果这是第一次调用，初始化状态变量 */
	if (lt6911uxe->current_plugin_state == -1) {
		lt6911uxe->current_plugin_state = present;
		lt6911uxe->plugin_check_count = 1;
		lt6911uxe->last_plugin_state = present;
		v4l2_info(sd, "%s: first call, state=%d\n", __func__, present);

		/* 第一次调用直接上报状态 */
		v4l2_ctrl_s_ctrl(lt6911uxe->detect_tx_5v_ctrl, present);

		/* 如果首次检测到插入状态，立即触发唤醒 */
		if (present) {
			rk_send_wakeup_key();
		}

		/* 继续检测，直到状态稳定 */
		schedule_delayed_work(&lt6911uxe->delayed_work_hotplug, msecs_to_jiffies(50));
		return;
	}

	/* 如果当前状态与上次检测到的状态一致，增加计数器 */
	if (present == lt6911uxe->current_plugin_state) {
		lt6911uxe->plugin_check_count++;
		v4l2_dbg(1, debug, sd, "%s: same state %d, count=%d\n",
			__func__, present, lt6911uxe->plugin_check_count);
	} else {
		/* 如果状态变化，重置计数器 */
		lt6911uxe->current_plugin_state = present;
		lt6911uxe->plugin_check_count = 1;
		v4l2_info(sd, "%s: state changed to %d, reset count\n", __func__, present);
	}

	/* 如果连续10次检测到相同状态，认为状态稳定 */
	if (lt6911uxe->plugin_check_count >= 10) {
		/* 如果状态与上次上报的状态不同，才上报新状态 */
		if (present != lt6911uxe->last_plugin_state) {
			v4l2_info(sd, "%s: reporting new stable state: %d\n", __func__, present);

			/* HDMI IN插入时触发系统唤醒 */
			if (present) {
				rk_send_wakeup_key();
			}

			v4l2_ctrl_s_ctrl(lt6911uxe->detect_tx_5v_ctrl, present);
			lt6911uxe->last_plugin_state = present;
            extcon_set_state_sync(lt6911uxe->extcon, EXTCON_JACK_VIDEO_IN, present);

			/* 当HDMI断开时，通知ALSA框架停止音频流 */
			if (!present) {
				/* 将声卡状态设置为断开状态 */
				int ret;
				struct snd_pcm_runtime *runtime;
				struct snd_pcm_substream *substream;

				v4l2_info(sd, "%s: HDMI disconnected, marking sound card as disconnected\n", __func__);

				/* 打印当前线程信息，以便调试 */
				v4l2_info(sd, "%s: current process: %s (%d)\n", __func__,
						 current->comm, current->pid);

				/* 检查全局 PCM 活动状态 */
				v4l2_info(sd, "%s: g_pcm_active=%d\n", __func__, atomic_read(&g_pcm_active));

				/* 如果全局 PCM 流处于活动状态，则将其设置为非活动 */
				if (atomic_read(&g_pcm_active)) {
					/* 设置全局 PCM 活动状态为非活动 */
					atomic_set(&g_pcm_active, 0);

					/* 检查全局 pcm_substream 是否有效 */
					v4l2_info(sd, "%s: g_pcm_substream=%p\n", __func__, g_pcm_substream);

					/* 获取全局 pcm_substream 指针 */
					substream = g_pcm_substream;

					if (substream) {
						/* 使用 snd_pcm_stream_lock_irq 函数锁定 PCM 流 */
						snd_pcm_stream_lock_irq(substream);

						runtime = substream->runtime;
						v4l2_info(sd, "%s: PCM substream found, state: %d\n", __func__,
								 runtime ? runtime->state : -1);

						/* 使用 snd_pcm_stop 函数将 PCM 流的状态设置为 DISCONNECTED */
						ret = snd_pcm_stop(substream, SNDRV_PCM_STATE_DISCONNECTED);
						v4l2_info(sd, "%s: snd_pcm_stop returned %d\n", __func__, ret);

						/* 再次检查状态 */
						if (runtime) {
							v4l2_info(sd, "%s: PCM state after stop: %d\n", __func__, runtime->state);

							/* 唤醒等待的进程 */
							wake_up(&runtime->sleep);
							wake_up(&runtime->tsleep);
						}

						/* 解锁 PCM 流 */
						snd_pcm_stream_unlock_irq(substream);
					} else {
						v4l2_info(sd, "%s: No PCM substream found\n", __func__);
					}
				} else {
					v4l2_info(sd, "%s: PCM stream not active\n", __func__);
				}
			}
		} else {
			v4l2_dbg(1, debug, sd, "%s: stable state %d unchanged, not reporting\n",
				__func__, present);
		}

		/* 重置计数器，准备下一次状态变化检测 */
		lt6911uxe->plugin_check_count = 0;
	} else {
		/* 如果状态不稳定，继续检测 */
		schedule_delayed_work(&lt6911uxe->delayed_work_hotplug, msecs_to_jiffies(50));
	}
}

static void lt6911uxe_delayed_work_res_change(struct work_struct *work)
{
	struct delayed_work *dwork = to_delayed_work(work);
	struct lt6911uxe *lt6911uxe = container_of(dwork,
			struct lt6911uxe, delayed_work_res_change);
	struct v4l2_subdev *sd = &lt6911uxe->sd;

	lt6911uxe_format_change(sd);
}

static int lt6911uxe_s_ctrl_detect_tx_5v(struct v4l2_subdev *sd)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);

	return v4l2_ctrl_s_ctrl(lt6911uxe->detect_tx_5v_ctrl,
			tx_5v_power_present(sd));
}

static int lt6911uxe_s_ctrl_audio_sampling_rate(struct v4l2_subdev *sd)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);

	return v4l2_ctrl_s_ctrl(lt6911uxe->audio_sampling_rate_ctrl,
			get_audio_sampling_rate(sd));
}

static int lt6911uxe_s_ctrl_audio_present(struct v4l2_subdev *sd)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);

	return v4l2_ctrl_s_ctrl(lt6911uxe->audio_present_ctrl,
			audio_present(sd));
}

static int lt6911uxe_update_controls(struct v4l2_subdev *sd)
{
	int ret = 0;

	ret |= lt6911uxe_s_ctrl_detect_tx_5v(sd);
	ret |= lt6911uxe_s_ctrl_audio_sampling_rate(sd);
	ret |= lt6911uxe_s_ctrl_audio_present(sd);

	return ret;
}

// static void lt6911uxe_config_dphy_timing(struct v4l2_subdev *sd)
// {
// 	u8 val;

// 	val = i2c_rd8(sd, CLK_ZERO_REG);
// 	i2c_wr8(sd, CLK_ZERO_REG, val);

// 	val = i2c_rd8(sd, HS_PREPARE_REG);
// 	i2c_wr8(sd, HS_PREPARE_REG, val);

// 	val = i2c_rd8(sd, HS_TRAIL);
// 	i2c_wr8(sd, HS_TRAIL, val);
// 	v4l2_info(sd, "%s: dphy timing: hs trail = %x\n", __func__, val);

// 	val = i2c_rd8(sd, MIPI_TX_PT0_TX0_DLY);
// 	i2c_wr8_and_or(sd, MIPI_TX_PT0_TX0_DLY, ~MIPI_TIMING_MASK, val);
// 	v4l2_info(sd, "%s: dphy timing: port0 tx0 delay = %x\n", __func__, val);

// 	val = i2c_rd8(sd, MIPI_TX_PT0_LPTX);
// 	i2c_wr8(sd, MIPI_TX_PT0_LPTX, val);
// 	v4l2_info(sd, "%s: dphy timing: port0 lptx = %x\n", __func__, val);

// 	v4l2_info(sd, "%s: dphy timing config done.\n", __func__);
// }

static inline void enable_stream(struct v4l2_subdev *sd, bool enable)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);

	if (enable) {
		// lt6911uxe_config_dphy_timing(sd);
		// usleep_range(5000, 6000);
		i2c_wr8(&lt6911uxe->sd, STREAM_CTL, ENABLE_STREAM);
	} else {
		i2c_wr8(&lt6911uxe->sd, STREAM_CTL, DISABLE_STREAM);
	}
	msleep(20);

	v4l2_info(sd, "%s: %sable\n",
		__func__, enable ? "en" : "dis");
}

static void lt6911uxe_format_change(struct v4l2_subdev *sd)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);
	struct v4l2_dv_timings timings;
	const struct v4l2_event lt6911uxe_ev_fmt = {
		.type = V4L2_EVENT_SOURCE_CHANGE,
		.u.src_change.changes = V4L2_EVENT_SRC_CH_RESOLUTION,
	};

	if (lt6911uxe_get_detected_timings(sd, &timings)) {
		enable_stream(sd, false);
		v4l2_dbg(1, debug, sd, "%s: No signal\n", __func__);
	}

	if (!v4l2_match_dv_timings(&lt6911uxe->timings, &timings, 0, false)) {
		enable_stream(sd, false);
		/* automatically set timing rather than set by user */
		lt6911uxe_s_dv_timings(sd, &timings);
		v4l2_print_dv_timings(sd->name,
				"Format_change: New format: ",
				&timings, false);
		if (sd->devnode && !lt6911uxe->i2c_client->irq)
			v4l2_subdev_notify_event(sd, &lt6911uxe_ev_fmt);
	}
	if (sd->devnode && lt6911uxe->i2c_client->irq)
		v4l2_subdev_notify_event(sd, &lt6911uxe_ev_fmt);
}

static int lt6911uxe_isr(struct v4l2_subdev *sd, u32 status, bool *handled)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);

	schedule_delayed_work(&lt6911uxe->delayed_work_res_change, HZ / 20);
	*handled = true;

	return 0;
}

static irqreturn_t lt6911uxe_res_change_irq_handler(int irq, void *dev_id)
{
	struct lt6911uxe *lt6911uxe = dev_id;
	bool handled;

	lt6911uxe_isr(&lt6911uxe->sd, 0, &handled);

	return handled ? IRQ_HANDLED : IRQ_NONE;
}

static int sample_rate[] = {32000, 44100, 48000, 96000, 176400, 192000};

static void lt6911uxe_audio_poll_work(struct work_struct *work)
{
    struct delayed_work *dwork = to_delayed_work(work);
    struct lt6911uxe *lt6911uxe = container_of(dwork, struct lt6911uxe, delayed_work_audio_poll);
    struct v4l2_subdev *sd = &lt6911uxe->sd;
    u8 fs_h, fs_l;
    u32 fs_value;

    /* 检查信号状态 */
    if (!tx_5v_power_present(sd)) {
        lt6911uxe->audio_polling_active = false;
        return;
    }

    /* 读取寄存器 */
    //lt6911uxe_i2c_enable(sd);
    fs_h = i2c_rd8(sd, AUDIO_FS_VALUE_H);
    fs_l = i2c_rd8(sd, AUDIO_FS_VALUE_L);
    //lt6911uxe_i2c_disable(sd);

    /* 合并采样率值 */
    fs_value = (fs_h << 8) | fs_l;
    for(int i = 0; i < sizeof(sample_rate)/ sizeof(int); i++) {
        if(abs(fs_value - sample_rate[i]/1000) <= 1) {
            lt6911uxe->audio_sampling_rate = sample_rate[i];
			lt6911uxe->is_audio_present = true;
            break;
        }
    }
    /* 根据文档说明，实际采样率需要乘以4 */
    //lt6911uxe->audio_sampling_rate = fs_value * 1000;
	//v4l2_info(sd, "sample_rate: %d\n", lt6911uxe->audio_sampling_rate);
    /* 重新调度工作队列 */
    if (lt6911uxe->audio_polling_active) {
        schedule_delayed_work(&lt6911uxe->delayed_work_audio_poll,
                            msecs_to_jiffies(200));
    }
}

static irqreturn_t plugin_detect_irq_handler(int irq, void *dev_id)
{
    struct lt6911uxe *lt6911uxe = dev_id;
    bool present = tx_5v_power_present(&lt6911uxe->sd);

    /* 打印当前检测到的状态 */
    v4l2_info(&lt6911uxe->sd, "%s: present:%d\n", __func__, present);

    if (present) {
        /* HDMI插入时启动轮询 */
        lt6911uxe->is_audio_present = false;
        if (!lt6911uxe->audio_polling_active) {
            lt6911uxe->audio_polling_active = true;
            schedule_delayed_work(&lt6911uxe->delayed_work_audio_poll,
                                msecs_to_jiffies(200));
        }
    } else {
        /* HDMI拔出时停止轮询 */
        lt6911uxe->is_audio_present = false;
        if (lt6911uxe->audio_polling_active) {
            lt6911uxe->audio_polling_active = false;
            cancel_delayed_work_sync(&lt6911uxe->delayed_work_audio_poll);
            lt6911uxe->audio_sampling_rate = 0; /* 重置采样率 */
        }
    }

    /* 取消之前的work，然后重新调度 */
    cancel_delayed_work(&lt6911uxe->delayed_work_hotplug);
    schedule_delayed_work(&lt6911uxe->delayed_work_hotplug, msecs_to_jiffies(50));

    return IRQ_HANDLED;
}

static void lt6911uxe_irq_poll_timer(struct timer_list *t)
{
	struct lt6911uxe *lt6911uxe = from_timer(lt6911uxe, t, timer);

	schedule_work(&lt6911uxe->work_i2c_poll);
	mod_timer(&lt6911uxe->timer, jiffies + msecs_to_jiffies(POLL_INTERVAL_MS));
}

static void lt6911uxe_work_i2c_poll(struct work_struct *work)
{
	struct lt6911uxe *lt6911uxe = container_of(work,
			struct lt6911uxe, work_i2c_poll);
	struct v4l2_subdev *sd = &lt6911uxe->sd;

	lt6911uxe_format_change(sd);
}

static int lt6911uxe_subscribe_event(struct v4l2_subdev *sd, struct v4l2_fh *fh,
				    struct v4l2_event_subscription *sub)
{
	switch (sub->type) {
	case V4L2_EVENT_SOURCE_CHANGE:
		return v4l2_src_change_event_subdev_subscribe(sd, fh, sub);
	case V4L2_EVENT_CTRL:
		return v4l2_ctrl_subdev_subscribe_event(sd, fh, sub);
	default:
		return -EINVAL;
	}
}

static int lt6911uxe_g_input_status(struct v4l2_subdev *sd, u32 *status)
{
	*status = 0;
	*status |= no_signal(sd) ? V4L2_IN_ST_NO_SIGNAL : 0;

	v4l2_dbg(1, debug, sd, "%s: status = 0x%x\n", __func__, *status);

	return 0;
}

static int lt6911uxe_s_dv_timings(struct v4l2_subdev *sd,
				 struct v4l2_dv_timings *timings)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);

	if (!timings)
		return -EINVAL;

	if (debug)
		v4l2_print_dv_timings(sd->name, "s_dv_timings: ",
				timings, false);

	if (v4l2_match_dv_timings(&lt6911uxe->timings, timings, 0, false)) {
		v4l2_dbg(1, debug, sd, "%s: no change\n", __func__);
		return 0;
	}

	lt6911uxe->timings = *timings;

	enable_stream(sd, false);

	return 0;
}

static int lt6911uxe_g_dv_timings(struct v4l2_subdev *sd,
				struct v4l2_dv_timings *timings)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);

	*timings = lt6911uxe->timings;

	return 0;
}

static int lt6911uxe_enum_dv_timings(struct v4l2_subdev *sd,
				struct v4l2_enum_dv_timings *timings)
{
	if (timings->pad != 0)
		return -EINVAL;

	return v4l2_enum_dv_timings_cap(timings,
			&lt6911uxe_timings_cap, NULL, NULL);
}

static int lt6911uxe_query_dv_timings(struct v4l2_subdev *sd,
				struct v4l2_dv_timings *timings)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);

	*timings = lt6911uxe->timings;
	if (debug)
		v4l2_print_dv_timings(sd->name,
				"query_dv_timings: ", timings, false);

	if (!v4l2_valid_dv_timings(timings, &lt6911uxe_timings_cap, NULL,
				NULL)) {
		v4l2_dbg(1, debug, sd, "%s: timings out of range\n",
				__func__);

		return -ERANGE;
	}

	return 0;
}

static int lt6911uxe_dv_timings_cap(struct v4l2_subdev *sd,
				struct v4l2_dv_timings_cap *cap)
{
	if (cap->pad != 0)
		return -EINVAL;

	*cap = lt6911uxe_timings_cap;

	return 0;
}

static int lt6911uxe_g_mbus_config(struct v4l2_subdev *sd,
			unsigned int pad, struct v4l2_mbus_config *cfg)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);

	cfg->type = lt6911uxe->bus_cfg.bus_type;
	cfg->bus.mipi_csi2 = lt6911uxe->bus_cfg.bus.mipi_csi2;

	return 0;
}

static int lt6911uxe_s_stream(struct v4l2_subdev *sd, int on)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);
	struct i2c_client *client = lt6911uxe->i2c_client;

	dev_info(&client->dev, "%s: on: %d, %dx%d%s%d\n", __func__, on,
				lt6911uxe->timings.bt.width, lt6911uxe->timings.bt.height,
				lt6911uxe->timings.bt.interlaced ? "I" : "P", lt6911uxe->cur_fps);
	enable_stream(sd, on);

	return 0;
}

static int lt6911uxe_enum_mbus_code(struct v4l2_subdev *sd,
			struct v4l2_subdev_state *sd_state,
			struct v4l2_subdev_mbus_code_enum *code)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);
	switch (code->index) {
	case 0:
		code->code = lt6911uxe->mbus_fmt_code;
		break;

	default:
		return -EINVAL;
	}

	return 0;
}

static int lt6911uxe_enum_frame_sizes(struct v4l2_subdev *sd,
				   struct v4l2_subdev_state *sd_state,
				   struct v4l2_subdev_frame_size_enum *fse)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);

	if (fse->index >= lt6911uxe->cfg_num)
		return -EINVAL;

	if (fse->code != lt6911uxe->mbus_fmt_code)
		return -EINVAL;

	fse->min_width  = lt6911uxe->support_modes[fse->index].width;
	fse->max_width  = lt6911uxe->support_modes[fse->index].width;
	fse->max_height = lt6911uxe->support_modes[fse->index].height;
	fse->min_height = lt6911uxe->support_modes[fse->index].height;

	return 0;
}

static int lt6911uxe_enum_frame_interval(struct v4l2_subdev *sd,
				struct v4l2_subdev_state *sd_state,
				struct v4l2_subdev_frame_interval_enum *fie)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);

	if (fie->index >= lt6911uxe->cfg_num)
		return -EINVAL;

	fie->code = lt6911uxe->mbus_fmt_code;

	fie->width = lt6911uxe->support_modes[fie->index].width;
	fie->height = lt6911uxe->support_modes[fie->index].height;
	fie->interval = lt6911uxe->support_modes[fie->index].max_fps;

	return 0;
}

static int lt6911uxe_get_reso_dist(const struct lt6911uxe_mode *mode,
				struct v4l2_dv_timings *timings)
{
	struct v4l2_bt_timings *bt = &timings->bt;
	u32 cur_fps, dist_fps;

	cur_fps = fps_calc(bt);
	dist_fps = DIV_ROUND_CLOSEST(mode->max_fps.denominator, mode->max_fps.numerator);

	return abs(mode->width - bt->width) +
		abs(mode->height - bt->height) + abs(dist_fps - cur_fps);
}

static const struct lt6911uxe_mode *
lt6911uxe_find_best_fit(struct lt6911uxe *lt6911uxe)
{
	int dist;
	int cur_best_fit = 0;
	int cur_best_fit_dist = -1;
	unsigned int i;

	for (i = 0; i < lt6911uxe->cfg_num; i++) {
		if (lt6911uxe->support_modes[i].interlace == lt6911uxe->timings.bt.interlaced) {
			dist = lt6911uxe_get_reso_dist(&lt6911uxe->support_modes[i],
							&lt6911uxe->timings);
			if (cur_best_fit_dist == -1 || dist < cur_best_fit_dist) {
				cur_best_fit_dist = dist;
				cur_best_fit = i;
			}
		}
	}
	//dev_info(&lt6911uxe->i2c_client->dev,
	//	"find current mode: support_mode[%d], %dx%d%s%dfps\n",
	//	cur_best_fit, lt6911uxe->support_modes[cur_best_fit].width,
	//	lt6911uxe->support_modes[cur_best_fit].height,
	//	lt6911uxe->support_modes[cur_best_fit].interlace ? "I" : "P",
	//	DIV_ROUND_CLOSEST(lt6911uxe->support_modes[cur_best_fit].max_fps.denominator,
	//	lt6911uxe->support_modes[cur_best_fit].max_fps.numerator));

	return &lt6911uxe->support_modes[cur_best_fit];
}

static int lt6911uxe_get_fmt(struct v4l2_subdev *sd,
			struct v4l2_subdev_state *sd_state,
			struct v4l2_subdev_format *format)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);
	const struct lt6911uxe_mode *mode;

	mutex_lock(&lt6911uxe->confctl_mutex);
	format->format.code = lt6911uxe->mbus_fmt_code;
	format->format.width = lt6911uxe->timings.bt.width;
	format->format.height = lt6911uxe->timings.bt.height;
	format->format.field =
		lt6911uxe->timings.bt.interlaced ?
		V4L2_FIELD_INTERLACED : V4L2_FIELD_NONE;
	format->format.colorspace = V4L2_COLORSPACE_SRGB;
	mutex_unlock(&lt6911uxe->confctl_mutex);

	mode = lt6911uxe_find_best_fit(lt6911uxe);
	lt6911uxe->cur_mode = mode;

	__v4l2_ctrl_s_ctrl_int64(lt6911uxe->pixel_rate,
				LT6911UXE_PIXEL_RATE);
	__v4l2_ctrl_s_ctrl(lt6911uxe->link_freq,
				mode->mipi_freq_idx);

	v4l2_dbg(1, debug, sd, "%s: mode->mipi_freq_idx(%d)", __func__, mode->mipi_freq_idx);

	v4l2_dbg(1, debug, sd, "%s: fmt code:%d, w:%d, h:%d, field code:%d\n",
			__func__, format->format.code, format->format.width,
			format->format.height, format->format.field);

	return 0;
}

static int lt6911uxe_set_fmt(struct v4l2_subdev *sd,
			struct v4l2_subdev_state *sd_state,
			struct v4l2_subdev_format *format)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);
	const struct lt6911uxe_mode *mode;

	/* is overwritten by get_fmt */
	u32 code = format->format.code;
	int ret = lt6911uxe_get_fmt(sd, sd_state, format);

	format->format.code = code;

	if (ret)
		return ret;

	switch (code) {
	case MEDIA_BUS_FMT_UYVY8_2X8:
		if (lt6911uxe->mbus_fmt_code == MEDIA_BUS_FMT_UYVY8_2X8)
			break;
		return -EINVAL;
	case MEDIA_BUS_FMT_BGR888_1X24:
		if (lt6911uxe->mbus_fmt_code == MEDIA_BUS_FMT_BGR888_1X24)
			break;
		return -EINVAL;
	case MEDIA_BUS_FMT_UV8_1X8:
		if (lt6911uxe->mbus_fmt_code == MEDIA_BUS_FMT_UV8_1X8)
			break;
		return -EINVAL;

	default:
		return -EINVAL;
	}

	if (format->which == V4L2_SUBDEV_FORMAT_TRY)
		return 0;

	lt6911uxe->mbus_fmt_code = format->format.code;
	mode = lt6911uxe_find_best_fit(lt6911uxe);
	lt6911uxe->cur_mode = mode;

	enable_stream(sd, false);

	return 0;
}

static int lt6911uxe_g_frame_interval(struct v4l2_subdev *sd,
			struct v4l2_subdev_frame_interval *fi)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);
	const struct lt6911uxe_mode *mode = lt6911uxe->cur_mode;

	mutex_lock(&lt6911uxe->confctl_mutex);
	fi->interval = mode->max_fps;
	mutex_unlock(&lt6911uxe->confctl_mutex);

	return 0;
}

static void lt6911uxe_get_module_inf(struct lt6911uxe *lt6911uxe,
				  struct rkmodule_inf *inf)
{
	memset(inf, 0, sizeof(*inf));
	strscpy(inf->base.sensor, LT6911UXE_NAME, sizeof(inf->base.sensor));
	strscpy(inf->base.module, lt6911uxe->module_name, sizeof(inf->base.module));
	strscpy(inf->base.lens, lt6911uxe->len_name, sizeof(inf->base.lens));
}

static long lt6911uxe_ioctl(struct v4l2_subdev *sd, unsigned int cmd, void *arg)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);
	long ret = 0;
	struct rkmodule_csi_dphy_param *dphy_param;
	struct rkmodule_capture_info  *capture_info;

	switch (cmd) {
	case RKMODULE_GET_MODULE_INFO:
		lt6911uxe_get_module_inf(lt6911uxe, (struct rkmodule_inf *)arg);
		break;
	case RKMODULE_GET_HDMI_MODE:
		*(int *)arg = RKMODULE_HDMIIN_MODE;
		break;
	case RKMODULE_SET_CSI_DPHY_PARAM:
		dphy_param = (struct rkmodule_csi_dphy_param *)arg;
		if (dphy_param->vendor == PHY_VENDOR_SAMSUNG)
			rk3588_dcphy_param = *dphy_param;
		dev_dbg(&lt6911uxe->i2c_client->dev,
			"sensor set dphy param\n");
		break;
	case RKMODULE_GET_CSI_DPHY_PARAM:
		dphy_param = (struct rkmodule_csi_dphy_param *)arg;
		*dphy_param = rk3588_dcphy_param;
		dev_dbg(&lt6911uxe->i2c_client->dev,
			"sensor get dphy param\n");
		break;
	case RKMODULE_GET_CAPTURE_MODE:
		capture_info = (struct rkmodule_capture_info *)arg;
		if (lt6911uxe->dual_mipi_port) {
			v4l2_dbg(1, debug, sd, "enable dual mipi mode\n");
			capture_info->mode = RKMODULE_MULTI_DEV_COMBINE_ONE;
			capture_info->multi_dev = lt6911uxe->multi_dev_info;
		} else {
			capture_info->mode = 0;
			capture_info->multi_dev = lt6911uxe->multi_dev_info;
		}
		break;
	default:
		ret = -ENOIOCTLCMD;
		break;
	}

	return ret;
}

static int lt6911uxe_s_power(struct v4l2_subdev *sd, int on)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);
	int ret = 0;

	mutex_lock(&lt6911uxe->confctl_mutex);

	if (lt6911uxe->power_on == !!on)
		goto unlock_and_return;

	if (on)
		lt6911uxe->power_on = true;
	else
		lt6911uxe->power_on = false;

unlock_and_return:
	mutex_unlock(&lt6911uxe->confctl_mutex);

	return ret;
}

#ifdef CONFIG_COMPAT
static long lt6911uxe_compat_ioctl32(struct v4l2_subdev *sd,
				  unsigned int cmd, unsigned long arg)
{
	void __user *up = compat_ptr(arg);
	struct rkmodule_inf *inf;
	long ret;
	int *seq;
	struct rkmodule_csi_dphy_param *dphy_param;
	struct rkmodule_capture_info  *capture_info;

	switch (cmd) {
	case RKMODULE_GET_MODULE_INFO:
		inf = kzalloc(sizeof(*inf), GFP_KERNEL);
		if (!inf) {
			ret = -ENOMEM;
			return ret;
		}

		ret = lt6911uxe_ioctl(sd, cmd, inf);
		if (!ret) {
			ret = copy_to_user(up, inf, sizeof(*inf));
			if (ret)
				ret = -EFAULT;
		}
		kfree(inf);
		break;
	case RKMODULE_GET_HDMI_MODE:
		seq = kzalloc(sizeof(*seq), GFP_KERNEL);
		if (!seq) {
			ret = -ENOMEM;
			return ret;
		}

		ret = lt6911uxe_ioctl(sd, cmd, seq);
		if (!ret) {
			ret = copy_to_user(up, seq, sizeof(*seq));
			if (ret)
				ret = -EFAULT;
		}
		kfree(seq);
		break;
	case RKMODULE_SET_CSI_DPHY_PARAM:
		dphy_param = kzalloc(sizeof(*dphy_param), GFP_KERNEL);
		if (!dphy_param) {
			ret = -ENOMEM;
			return ret;
		}

		ret = copy_from_user(dphy_param, up, sizeof(*dphy_param));
		if (!ret)
			ret = lt6911uxe_ioctl(sd, cmd, dphy_param);
		else
			ret = -EFAULT;
		kfree(dphy_param);
		break;
	case RKMODULE_GET_CSI_DPHY_PARAM:
		dphy_param = kzalloc(sizeof(*dphy_param), GFP_KERNEL);
		if (!dphy_param) {
			ret = -ENOMEM;
			return ret;
		}

		ret = lt6911uxe_ioctl(sd, cmd, dphy_param);
		if (!ret) {
			ret = copy_to_user(up, dphy_param, sizeof(*dphy_param));
			if (ret)
				ret = -EFAULT;
		}
		kfree(dphy_param);
		break;
	case RKMODULE_GET_CAPTURE_MODE:
		capture_info = kzalloc(sizeof(*capture_info), GFP_KERNEL);
		if (!capture_info) {
			ret = -ENOMEM;
			return ret;
		}

		ret = lt6911uxe_ioctl(sd, cmd, capture_info);
		if (!ret) {
			ret = copy_to_user(up, capture_info, sizeof(*capture_info));
			if (ret)
				ret = -EFAULT;
		}
		kfree(capture_info);
		break;
	default:
		ret = -ENOIOCTLCMD;
		break;
	}

	return ret;
}
#endif

#ifdef CONFIG_VIDEO_V4L2_SUBDEV_API
static int lt6911uxe_open(struct v4l2_subdev *sd, struct v4l2_subdev_fh *fh)
{
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);
	struct v4l2_mbus_framefmt *try_fmt =
				v4l2_subdev_get_try_format(sd, fh->state, 0);
	const struct lt6911uxe_mode *def_mode = &lt6911uxe->support_modes[0];

	mutex_lock(&lt6911uxe->confctl_mutex);
	/* Initialize try_fmt */
	try_fmt->width = def_mode->width;
	try_fmt->height = def_mode->height;
	try_fmt->code = lt6911uxe->mbus_fmt_code;
	try_fmt->field = V4L2_FIELD_NONE;
	mutex_unlock(&lt6911uxe->confctl_mutex);

	return 0;
}
#endif

#ifdef CONFIG_VIDEO_V4L2_SUBDEV_API
static const struct v4l2_subdev_internal_ops lt6911uxe_internal_ops = {
	.open = lt6911uxe_open,
};
#endif

static const struct v4l2_subdev_core_ops lt6911uxe_core_ops = {
	.s_power = lt6911uxe_s_power,
	.interrupt_service_routine = lt6911uxe_isr,
	.subscribe_event = lt6911uxe_subscribe_event,
	.unsubscribe_event = v4l2_event_subdev_unsubscribe,
	.ioctl = lt6911uxe_ioctl,
#ifdef CONFIG_COMPAT
	.compat_ioctl32 = lt6911uxe_compat_ioctl32,
#endif
};

static const struct v4l2_subdev_video_ops lt6911uxe_video_ops = {
	.g_input_status = lt6911uxe_g_input_status,
	.s_dv_timings = lt6911uxe_s_dv_timings,
	.g_dv_timings = lt6911uxe_g_dv_timings,
	.query_dv_timings = lt6911uxe_query_dv_timings,
	.s_stream = lt6911uxe_s_stream,
	.g_frame_interval = lt6911uxe_g_frame_interval,
};

static const struct v4l2_subdev_pad_ops lt6911uxe_pad_ops = {
	.enum_mbus_code = lt6911uxe_enum_mbus_code,
	.enum_frame_size = lt6911uxe_enum_frame_sizes,
	.enum_frame_interval = lt6911uxe_enum_frame_interval,
	.set_fmt = lt6911uxe_set_fmt,
	.get_fmt = lt6911uxe_get_fmt,
	.enum_dv_timings = lt6911uxe_enum_dv_timings,
	.dv_timings_cap = lt6911uxe_dv_timings_cap,
	.get_mbus_config = lt6911uxe_g_mbus_config,
};

static const struct v4l2_subdev_ops lt6911uxe_ops = {
	.core = &lt6911uxe_core_ops,
	.video = &lt6911uxe_video_ops,
	.pad = &lt6911uxe_pad_ops,
};

static const struct v4l2_ctrl_config lt6911uxe_ctrl_audio_sampling_rate = {
	.id = RK_V4L2_CID_AUDIO_SAMPLING_RATE,
	.name = "Audio sampling rate",
	.type = V4L2_CTRL_TYPE_INTEGER,
	.min = 0,
	.max = 768000,
	.step = 1,
	.def = 0,
	.flags = V4L2_CTRL_FLAG_READ_ONLY,
};

static const struct v4l2_ctrl_config lt6911uxe_ctrl_audio_present = {
	.id = RK_V4L2_CID_AUDIO_PRESENT,
	.name = "Audio present",
	.type = V4L2_CTRL_TYPE_BOOLEAN,
	.min = 0,
	.max = 1,
	.step = 1,
	.def = 0,
	.flags = V4L2_CTRL_FLAG_READ_ONLY,
};

static void lt6911uxe_reset(struct lt6911uxe *lt6911uxe)
{
	gpiod_set_value(lt6911uxe->reset_gpio, 0);
	usleep_range(2000, 2100);
	gpiod_set_value(lt6911uxe->reset_gpio, 1);
	usleep_range(120*1000, 121*1000);
	gpiod_set_value(lt6911uxe->reset_gpio, 0);
	usleep_range(300*1000, 310*1000);
}

static int lt6911uxe_init_v4l2_ctrls(struct lt6911uxe *lt6911uxe)
{
	const struct lt6911uxe_mode *mode;
	struct v4l2_subdev *sd;
	int ret;

	mode = lt6911uxe->cur_mode;
	sd = &lt6911uxe->sd;
	ret = v4l2_ctrl_handler_init(&lt6911uxe->hdl, 5);
	if (ret)
		return ret;

	lt6911uxe->link_freq = v4l2_ctrl_new_int_menu(&lt6911uxe->hdl, NULL,
			V4L2_CID_LINK_FREQ,
			ARRAY_SIZE(link_freq_menu_items) - 1, 0,
			link_freq_menu_items);
	lt6911uxe->pixel_rate = v4l2_ctrl_new_std(&lt6911uxe->hdl, NULL,
			V4L2_CID_PIXEL_RATE,
			0, LT6911UXE_PIXEL_RATE, 1, LT6911UXE_PIXEL_RATE);

	lt6911uxe->detect_tx_5v_ctrl = v4l2_ctrl_new_std(&lt6911uxe->hdl,
			NULL, V4L2_CID_DV_RX_POWER_PRESENT,
			0, 1, 0, 0);

	lt6911uxe->audio_sampling_rate_ctrl =
		v4l2_ctrl_new_custom(&lt6911uxe->hdl,
				&lt6911uxe_ctrl_audio_sampling_rate, NULL);
	lt6911uxe->audio_present_ctrl = v4l2_ctrl_new_custom(&lt6911uxe->hdl,
			&lt6911uxe_ctrl_audio_present, NULL);

	sd->ctrl_handler = &lt6911uxe->hdl;
	if (lt6911uxe->hdl.error) {
		ret = lt6911uxe->hdl.error;
		v4l2_err(sd, "cfg v4l2 ctrls failed! ret:%d\n", ret);
		return ret;
	}

	__v4l2_ctrl_s_ctrl(lt6911uxe->link_freq, mode->mipi_freq_idx);
	__v4l2_ctrl_s_ctrl_int64(lt6911uxe->pixel_rate, LT6911UXE_PIXEL_RATE);

	if (lt6911uxe_update_controls(sd)) {
		ret = -ENODEV;
		v4l2_err(sd, "update v4l2 ctrls failed! ret:%d\n", ret);
		return ret;
	}

	return 0;
}

#ifdef CONFIG_OF
static int lt6911uxe_probe_of(struct lt6911uxe *lt6911uxe)
{
	struct device *dev = &lt6911uxe->i2c_client->dev;
	struct device_node *node = dev->of_node;
	struct device_node *ep;
	int ret;

	ret = of_property_read_u32(node, RKMODULE_CAMERA_MODULE_INDEX,
			&lt6911uxe->module_index);
	ret |= of_property_read_string(node, RKMODULE_CAMERA_MODULE_FACING,
			&lt6911uxe->module_facing);
	ret |= of_property_read_string(node, RKMODULE_CAMERA_MODULE_NAME,
			&lt6911uxe->module_name);
	ret |= of_property_read_string(node, RKMODULE_CAMERA_LENS_NAME,
			&lt6911uxe->len_name);
	if (ret) {
		dev_err(dev, "could not get module information!\n");
		return -EINVAL;
	}

	lt6911uxe->power_gpio = devm_gpiod_get_optional(dev, "power",
			GPIOD_OUT_LOW);
	if (IS_ERR(lt6911uxe->power_gpio)) {
		dev_err(dev, "failed to get power gpio\n");
		ret = PTR_ERR(lt6911uxe->power_gpio);
		return ret;
	}

	lt6911uxe->reset_gpio = devm_gpiod_get_optional(dev, "reset",
			GPIOD_OUT_HIGH);
	if (IS_ERR(lt6911uxe->reset_gpio)) {
		dev_err(dev, "failed to get reset gpio\n");
		ret = PTR_ERR(lt6911uxe->reset_gpio);
		return ret;
	}

	lt6911uxe->plugin_det_gpio = devm_gpiod_get_optional(dev, "plugin-det",
			GPIOD_IN);
	if (IS_ERR(lt6911uxe->plugin_det_gpio)) {
		dev_err(dev, "failed to get plugin det gpio\n");
		ret = PTR_ERR(lt6911uxe->plugin_det_gpio);
		return ret;
	}
	if (of_property_read_bool(dev->of_node, "output-rgb"))
		lt6911uxe->rgb_out = true;

	ep = of_graph_get_next_endpoint(dev->of_node, NULL);
	if (!ep) {
		dev_err(dev, "missing endpoint node\n");
		return -EINVAL;
	}

	ret = v4l2_fwnode_endpoint_parse(of_fwnode_handle(ep),
					&lt6911uxe->bus_cfg);
	if (ret) {
		dev_err(dev, "failed to parse endpoint\n");
		goto put_node;
	}

	lt6911uxe->support_modes = supported_modes_dphy;
	lt6911uxe->cfg_num = ARRAY_SIZE(supported_modes_dphy);

	lt6911uxe->xvclk = devm_clk_get(dev, "xvclk");
	if (IS_ERR(lt6911uxe->xvclk)) {
		dev_err(dev, "failed to get xvclk\n");
		ret = -EINVAL;
		goto put_node;
	}

	ret = clk_prepare_enable(lt6911uxe->xvclk);
	if (ret) {
		dev_err(dev, "Failed! to enable xvclk\n");
		goto put_node;
	}

	lt6911uxe->enable_hdcp = false;

	gpiod_set_value(lt6911uxe->power_gpio, 1);
	lt6911uxe_reset(lt6911uxe);

	ret = 0;

put_node:
	of_node_put(ep);
	return ret;
}
#else
static inline int lt6911uxe_probe_of(struct lt6911uxe *state)
{
	return -ENODEV;
}
#endif
static int lt6911uxe_check_chip_id(struct lt6911uxe *lt6911uxe)
{
	struct device *dev = &lt6911uxe->i2c_client->dev;
	struct v4l2_subdev *sd = &lt6911uxe->sd;
	u8 id_h, id_l;
	u32 chipid;
	int ret = 0;

	lt6911uxe_i2c_enable(sd);
	id_l  = i2c_rd8(sd, CHIPID_REGL);
	id_h  = i2c_rd8(sd, CHIPID_REGH);
	lt6911uxe_i2c_disable(sd);

	chipid = (id_h << 8) | id_l;
	if (chipid != LT6911UXE_CHIPID) {
		dev_err(dev, "chipid err, read:%#x, expect:%#x\n",
				chipid, LT6911UXE_CHIPID);
		return -EINVAL;
	}
	dev_info(dev, "check chipid ok, id:%#x", chipid);

	return ret;
}

static int lt6911uxe_get_multi_dev_info(struct lt6911uxe *lt6911uxe)
{
	struct device *dev = &lt6911uxe->i2c_client->dev;
	struct device_node *node = dev->of_node;
	struct device_node *multi_info_np;

	lt6911uxe->dual_mipi_port = false;
	multi_info_np = of_get_child_by_name(node, "multi-dev-info");
	if (!multi_info_np) {
		dev_info(dev, "failed to get multi dev info\n");
		return -EINVAL;
	}

	of_property_read_u32(multi_info_np, "dev-idx-l",
			&lt6911uxe->multi_dev_info.dev_idx[0]);
	of_property_read_u32(multi_info_np, "dev-idx-r",
			&lt6911uxe->multi_dev_info.dev_idx[1]);
	of_property_read_u32(multi_info_np, "combine-idx",
			&lt6911uxe->multi_dev_info.combine_idx[0]);
	of_property_read_u32(multi_info_np, "pixel-offset",
			&lt6911uxe->multi_dev_info.pixel_offset);
	of_property_read_u32(multi_info_np, "dev-num",
			&lt6911uxe->multi_dev_info.dev_num);

	lt6911uxe->dual_mipi_port = true;
	dev_info(dev,
		"multi dev left: mipi%d, multi dev right: mipi%d, combile mipi%d, dev num: %d\n",
		lt6911uxe->multi_dev_info.dev_idx[0], lt6911uxe->multi_dev_info.dev_idx[1],
		lt6911uxe->multi_dev_info.combine_idx[0], lt6911uxe->multi_dev_info.dev_num);

	return 0;
}

static int lt6911uxe_probe(struct i2c_client *client,
			  const struct i2c_device_id *id)
{
	struct v4l2_dv_timings default_timing =
				V4L2_DV_BT_CEA_640X480P59_94;
	struct lt6911uxe *lt6911uxe;
	struct v4l2_subdev *sd;
	struct device *dev = &client->dev;
	char facing[2];
	int err;

	dev_info(dev, "driver version: %02x.%02x.%02x",
		DRIVER_VERSION >> 16,
		(DRIVER_VERSION & 0xff00) >> 8,
		DRIVER_VERSION & 0x00ff);

	lt6911uxe = devm_kzalloc(dev, sizeof(struct lt6911uxe), GFP_KERNEL);
	if (!lt6911uxe)
		return -ENOMEM;

	sd = &lt6911uxe->sd;
	lt6911uxe->i2c_client = client;
	// lt6911uxe->mbus_fmt_code = LT6911UXE_MEDIA_BUS_FMT;

	err = lt6911uxe_probe_of(lt6911uxe);
	if (err) {
		v4l2_err(sd, "lt6911uxe_parse_of failed! err:%d\n", err);
		return err;
	}

	lt6911uxe->timings = default_timing;
	lt6911uxe->cur_mode = &lt6911uxe->support_modes[0];
	if (lt6911uxe->rgb_out)
		lt6911uxe->mbus_fmt_code = MEDIA_BUS_FMT_BGR888_1X24;
	else
		lt6911uxe->mbus_fmt_code = MEDIA_BUS_FMT_UYVY8_2X8;
	err = lt6911uxe_get_multi_dev_info(lt6911uxe);
	if (err)
		v4l2_info(sd, "get multi dev info failed, not use dual mipi mode\n");

	err = lt6911uxe_check_chip_id(lt6911uxe);
	if (err < 0)
		return err;

	mutex_init(&lt6911uxe->confctl_mutex);
	err = lt6911uxe_init_v4l2_ctrls(lt6911uxe);
	if (err)
		goto err_free_hdl;

	client->flags |= I2C_CLIENT_SCCB;
#ifdef CONFIG_VIDEO_V4L2_SUBDEV_API
	v4l2_i2c_subdev_init(sd, client, &lt6911uxe_ops);
	sd->internal_ops = &lt6911uxe_internal_ops;
	sd->flags |= V4L2_SUBDEV_FL_HAS_DEVNODE | V4L2_SUBDEV_FL_HAS_EVENTS;
#endif

#if defined(CONFIG_MEDIA_CONTROLLER)
	lt6911uxe->pad.flags = MEDIA_PAD_FL_SOURCE;
	sd->entity.function = MEDIA_ENT_F_CAM_SENSOR;
	err = media_entity_pads_init(&sd->entity, 1, &lt6911uxe->pad);
	if (err < 0) {
		v4l2_err(sd, "media entity init failed! err:%d\n", err);
		goto err_free_hdl;
	}
#endif
	memset(facing, 0, sizeof(facing));
	if (strcmp(lt6911uxe->module_facing, "back") == 0)
		facing[0] = 'b';
	else
		facing[0] = 'f';

	snprintf(sd->name, sizeof(sd->name), "m%02d_%s_%s %s",
		 lt6911uxe->module_index, facing,
		 LT6911UXE_NAME, dev_name(sd->dev));
	err = v4l2_async_register_subdev_sensor(sd);
	if (err < 0) {
		v4l2_err(sd, "v4l2 register subdev failed! err:%d\n", err);
		goto err_clean_entity;
	}

	INIT_DELAYED_WORK(&lt6911uxe->delayed_work_hotplug,
			lt6911uxe_delayed_work_hotplug);
	INIT_DELAYED_WORK(&lt6911uxe->delayed_work_res_change,
			lt6911uxe_delayed_work_res_change);
    INIT_DELAYED_WORK(&lt6911uxe->delayed_work_audio_poll, lt6911uxe_audio_poll_work);
    lt6911uxe->audio_polling_active = false;

    /* 初始化插拔状态检测相关变量 */
    lt6911uxe->last_plugin_state = -1;
    lt6911uxe->current_plugin_state = -1;
    lt6911uxe->plugin_check_count = 0;
    lt6911uxe->debounce_active = false;

    /* 初始化 PCM 相关变量 */
    atomic_set(&lt6911uxe->pcm_active, 0);
    spin_lock_init(&lt6911uxe->pcm_lock);

	if (lt6911uxe->i2c_client->irq) {
		v4l2_dbg(1, debug, sd, "cfg lt6911uxe irq!\n");
		err = devm_request_threaded_irq(dev,
				lt6911uxe->i2c_client->irq,
				NULL, lt6911uxe_res_change_irq_handler,
				IRQF_TRIGGER_RISING | IRQF_ONESHOT,
				"lt6911uxe", lt6911uxe);
		if (err) {
			v4l2_err(sd, "request irq failed! err:%d\n", err);
			goto err_work_queues;
		}
	} else {
		v4l2_dbg(1, debug, sd, "no irq, cfg poll!\n");
		INIT_WORK(&lt6911uxe->work_i2c_poll, lt6911uxe_work_i2c_poll);
		timer_setup(&lt6911uxe->timer, lt6911uxe_irq_poll_timer, 0);
		lt6911uxe->timer.expires = jiffies +
				       msecs_to_jiffies(POLL_INTERVAL_MS);
		add_timer(&lt6911uxe->timer);
	}

	// i2c_set_clientdata(client, lt6911uxe);

	//(void)soc_codec_dev_lt6911uxe;
	//(void)lt6911uxe_dai;
	err = devm_snd_soc_register_component(&client->dev, &soc_codec_dev_lt6911uxe, &lt6911uxe_dai, 1);
	/* 设置 drvdata，以便在 ALSA SoC 回调函数中可以正确地获取 lt6911uxe 结构体指针 */
	// dev_set_drvdata(&client->dev, lt6911uxe);
    dev_info(&client->dev, "%s register snd soc codec result: %d\n", __func__, err);
	lt6911uxe->extcon = devm_extcon_dev_allocate(dev, lt6911uxe_hdmi_cable);
	if (IS_ERR(lt6911uxe->extcon)) {
		dev_err(dev, "allocate extcon failed\n");
		goto err_work_queues;
	}

	err = devm_extcon_dev_register(dev, lt6911uxe->extcon);
	if(err) {
		dev_err(dev, "failed to register extcon: %d\n", err);
		goto err_work_queues;
	}
	dev_info(dev, "lxb: dev_set_drvdata\n");

    err = sysfs_create_group(&dev->kobj, &lt6911uxe_attribute_group);
    if (err){
        goto err_work_queues;
    }

	lt6911uxe->plugin_irq = gpiod_to_irq(lt6911uxe->plugin_det_gpio);
	if (lt6911uxe->plugin_irq < 0)
		dev_err(dev, "failed to get plugin det irq, maybe no use\n");

	err = devm_request_threaded_irq(dev, lt6911uxe->plugin_irq, NULL,
			plugin_detect_irq_handler, IRQF_TRIGGER_FALLING |
			IRQF_TRIGGER_RISING | IRQF_ONESHOT, "lt6911uxe",
			lt6911uxe);
	if (err)
		dev_err(dev, "failed to register plugin det irq (%d), maybe no use\n", err);

	err = v4l2_ctrl_handler_setup(sd->ctrl_handler);
	if (err) {
		v4l2_err(sd, "v4l2 ctrl handler setup failed! err:%d\n", err);
		goto err_work_queues;
	}
	// enable_stream(sd, false);
	schedule_delayed_work(&lt6911uxe->delayed_work_res_change, 50);

	/* 主动调度一次hotplug检测，确保首次烧录时能正确检测状态 */
	schedule_delayed_work(&lt6911uxe->delayed_work_hotplug, msecs_to_jiffies(100));

	if (tx_5v_power_present(sd)) {
		v4l2_info(sd, "HDMI already connected at boot, starting audio polling\n");
		lt6911uxe->is_audio_present = false;
		lt6911uxe->audio_polling_active = true;
		extcon_set_state_sync(lt6911uxe->extcon, EXTCON_JACK_VIDEO_IN, true);
		schedule_delayed_work(&lt6911uxe->delayed_work_audio_poll, msecs_to_jiffies(200));
	}
	v4l2_info(sd, "%s found @ 0x%x (%s)\n", client->name,
			client->addr << 1, client->adapter->name);

	return 0;

err_work_queues:
	if (!lt6911uxe->i2c_client->irq)
		flush_work(&lt6911uxe->work_i2c_poll);
	cancel_delayed_work(&lt6911uxe->delayed_work_hotplug);
	cancel_delayed_work(&lt6911uxe->delayed_work_res_change);
err_clean_entity:
#if defined(CONFIG_MEDIA_CONTROLLER)
	media_entity_cleanup(&sd->entity);
#endif
err_free_hdl:
	v4l2_ctrl_handler_free(&lt6911uxe->hdl);
	mutex_destroy(&lt6911uxe->confctl_mutex);
	return err;
}

static void lt6911uxe_remove(struct i2c_client *client)
{
	struct v4l2_subdev *sd = i2c_get_clientdata(client);
	struct lt6911uxe *lt6911uxe = to_lt6911uxe(sd);

	if (!lt6911uxe->i2c_client->irq) {
		del_timer_sync(&lt6911uxe->timer);
		flush_work(&lt6911uxe->work_i2c_poll);
	}
	cancel_delayed_work_sync(&lt6911uxe->delayed_work_audio_poll);
	cancel_delayed_work_sync(&lt6911uxe->delayed_work_hotplug);
	cancel_delayed_work_sync(&lt6911uxe->delayed_work_res_change);
	v4l2_async_unregister_subdev(sd);
	v4l2_device_unregister_subdev(sd);
#if defined(CONFIG_MEDIA_CONTROLLER)
	media_entity_cleanup(&sd->entity);
#endif
	v4l2_ctrl_handler_free(&lt6911uxe->hdl);
	mutex_destroy(&lt6911uxe->confctl_mutex);
	clk_disable_unprepare(lt6911uxe->xvclk);
}

#if IS_ENABLED(CONFIG_OF)
static const struct of_device_id lt6911uxe_of_match[] = {
	{ .compatible = "lontium,lt6911uxe" },
	{},
};
MODULE_DEVICE_TABLE(of, lt6911uxe_of_match);
#endif

static struct i2c_driver lt6911uxe_driver = {
	.driver = {
		.name = LT6911UXE_NAME,
		.of_match_table = of_match_ptr(lt6911uxe_of_match),
	},
	.probe = lt6911uxe_probe,
	.remove = lt6911uxe_remove,
};

static int __init lt6911uxe_driver_init(void)
{
	return i2c_add_driver(&lt6911uxe_driver);
}

static void __exit lt6911uxe_driver_exit(void)
{
	i2c_del_driver(&lt6911uxe_driver);
}

device_initcall_sync(lt6911uxe_driver_init);
module_exit(lt6911uxe_driver_exit);

MODULE_DESCRIPTION("Lontium lt6911uxe HDMI to CSI-2 bridge driver");
MODULE_AUTHOR("Jianwei Fan <<EMAIL>>");
MODULE_LICENSE("GPL");
